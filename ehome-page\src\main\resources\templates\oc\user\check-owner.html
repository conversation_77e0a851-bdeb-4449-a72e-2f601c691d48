<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('业主管理')" />
</head>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="config-form">
					<div class="select-list">
						<ul>
							<li>
								姓名：<input type="text" name="owner_name" onkeypress="if(event.keyCode==13) $.table.search()"/>
							</li>
							<li>
								手机号码：<input type="text" name="mobile" onkeypress="if(event.keyCode==13) $.table.search()"/>
							</li>
							<li>
								住户状态：<select name="is_live">
									<option value="">全部</option>
									<option value="1">已入住</option>
									<option value="0">未入住</option>
								</select>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
			
	        <div class="btn-group-sm" id="toolbar" role="group">
		        <a class="btn btn-danger ml-10 multiple disabled" onclick="$.operate.removeAll()">
		            <i class="fa fa-remove"></i> 批量通过
		        </a>
	        </div>
	        <div class="col-sm-12 select-table table-striped">
	            <table id="bootstrap-table"></table>
	        </div>
	    </div>
	</div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/owner";

        $(function() {
            var options = {
                url: prefix + "/checkList",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "业主",
				layer:{
					area:['800px','550px'],
					offset: '70px'
				},
                columns: [{
                    checkbox: true
                },
                {
                    field: 'owner_id',
                    title: '业主ID',
                    visible: false
                },
                {
                    field: 'owner_name',
                    title: '姓名',
                    formatter: function(value, row, index) {
                        return '<a href="javascript:void(0)" onclick="viewDetail(\'' + row.owner_id + '\')" style="color: #007bff;">' + value + '</a>';
                    }
                },
				{
					field: 'gender',
					title: '性别',
					visible: false,
					formatter: function(value, row, index) {
						if (value == 'M') return '男';
						else if (value == 'F') return '女';
						return '-';
					}
				},
                {
                    field: 'mobile',
                    title: '手机号'
                },{
					field: 'house_count',
                    title: '房屋数',
					visible: false,
					formatter: function(value, row, index) {
						return value + '套';
					}
                },{
                    field: 'house_info',
                    title: '房屋信息',
                    formatter: function(value, row, index) {
                        var houseInfo = value || '-';
                        return houseInfo;
                    }
                },
				{
					field: 'is_live',
					title: '入住状态',
					formatter: function(value, row, index) {
						if (value == '1') return '已入住';
						else if (value == '0') return '未入住';
						return '-';
					}
				},
				{
					field: 'role',
					title: '人员角色',
					formatter: function(value, row, index) {
						var roleDatas = [
							{ dictValue: "1", dictLabel: "业主" },
							{ dictValue: "2", dictLabel: "家庭成员" },
							{ dictValue: "3", dictLabel: "租户" }
						];
						return $.table.selectDictLabel(roleDatas, value) || '-';
					}
				},
                {
                    field: 'remark',
                    title: '备注',
                    align: 'center',
					formatter: function(value, row, index) {
						return value == null ? '-' : value.substring(0, 10);
					}
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
						actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.owner_id + '\')"><i class="fa fa-edit"></i> 通过</a> ');
                        actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.owner_id + '\')"><i class="fa fa-remove"></i> 不通过</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>